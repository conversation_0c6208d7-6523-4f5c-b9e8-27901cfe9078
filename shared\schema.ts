import { pgTable, text, serial, timestamp, integer, boolean, jsonb } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const processedImages = pgTable("processed_images", {
  id: serial("id").primaryKey(),
  originalFileName: text("original_file_name").notNull(),
  processedFileName: text("processed_file_name"),
  fileSize: integer("file_size").notNull(),
  mimeType: text("mime_type").notNull(),
  status: text("status").notNull().default("pending"), // pending, processing, completed, failed
  processingTime: integer("processing_time"), // in milliseconds
  errorMessage: text("error_message"),
  metadata: jsonb("metadata"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
});

export const insertProcessedImageSchema = createInsertSchema(processedImages).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

export type InsertProcessedImage = z.infer<typeof insertProcessedImageSchema>;
export type ProcessedImage = typeof processedImages.$inferSelect;

// API response types
export const watermarkRemovalResponseSchema = z.object({
  success: z.boolean(),
  processedImageUrl: z.string().optional(),
  processingTime: z.number().optional(),
  error: z.string().optional()
});

export type WatermarkRemovalResponse = z.infer<typeof watermarkRemovalResponseSchema>;
