import { useState, useCallback } from 'react';
import { useMutation, useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import type { ProcessingImageItem, WatermarkRemovalResponse, ProcessingStatus } from '@/types/api';

export const useWatermarkRemoval = () => {
  const [processingImages, setProcessingImages] = useState<ProcessingImageItem[]>([]);

  const uploadMutation = useMutation({
    mutationFn: async (file: File): Promise<WatermarkRemovalResponse> => {
      const formData = new FormData();
      formData.append('image', file);
      
      const response = await apiRequest('POST', '/api/watermark-removal', formData);
      return response.json();
    },
    onSuccess: (data, file) => {
      setProcessingImages(prev =>
        prev.map(img =>
          img.file === file
            ? {
                ...img,
                id: data.id.toString(),
                status: data.success ? 'completed' : 'failed',
                processedImageUrl: data.processedImageUrl,
                processingTime: data.processingTime,
                error: data.error,
                isDemoMode: data.error && (data.error.includes('demo mode') || data.error.includes('Demo mode') || data.error.includes('API unavailable'))
              }
            : img
        )
      );
    },
    onError: (error, file) => {
      setProcessingImages(prev => 
        prev.map(img => 
          img.file === file 
            ? { 
                ...img, 
                status: 'failed',
                error: error instanceof Error ? error.message : 'Upload failed'
              }
            : img
        )
      );
    },
  });

  const addFiles = useCallback((files: File[]) => {
    const newImages: ProcessingImageItem[] = files.map(file => ({
      id: `temp-${Date.now()}-${Math.random()}`,
      file,
      status: 'pending',
      preview: URL.createObjectURL(file)
    }));

    setProcessingImages(prev => [...prev, ...newImages]);

    // Start processing each file
    newImages.forEach(image => {
      setProcessingImages(prev => 
        prev.map(img => 
          img.id === image.id 
            ? { ...img, status: 'processing' }
            : img
        )
      );
      
      uploadMutation.mutate(image.file);
    });
  }, [uploadMutation]);

  const removeImage = useCallback((id: string) => {
    setProcessingImages(prev => {
      const image = prev.find(img => img.id === id);
      if (image) {
        URL.revokeObjectURL(image.preview);
      }
      return prev.filter(img => img.id !== id);
    });
  }, []);

  const retryImage = useCallback((id: string) => {
    const image = processingImages.find(img => img.id === id);
    if (image) {
      setProcessingImages(prev => 
        prev.map(img => 
          img.id === id 
            ? { ...img, status: 'processing', error: undefined }
            : img
        )
      );
      uploadMutation.mutate(image.file);
    }
  }, [processingImages, uploadMutation]);

  const clearAll = useCallback(() => {
    processingImages.forEach(image => {
      URL.revokeObjectURL(image.preview);
    });
    setProcessingImages([]);
  }, [processingImages]);

  return {
    processingImages,
    addFiles,
    removeImage,
    retryImage,
    clearAll,
    isUploading: uploadMutation.isPending,
  };
};

export const useProcessingStatus = (imageId: number | null) => {
  return useQuery({
    queryKey: ['/api/watermark-removal', imageId],
    enabled: !!imageId,
    refetchInterval: (data) => {
      // Stop refetching if completed or failed
      if (data?.status === 'completed' || data?.status === 'failed') {
        return false;
      }
      return 2000; // Refetch every 2 seconds
    },
  });
};
