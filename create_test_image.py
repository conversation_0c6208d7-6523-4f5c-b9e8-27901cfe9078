from PIL import Image, ImageDraw, ImageFont
import os

# Create a simple test image
width, height = 400, 300
image = Image.new('RGB', (width, height), color='lightblue')
draw = ImageDraw.Draw(image)

# Try to use a default font, fallback to default if not available
try:
    font = ImageFont.truetype("arial.ttf", 20)
except:
    font = ImageFont.load_default()

# Draw some text
draw.text((50, 100), "Test Image", fill='black', font=font)
draw.text((150, 200), "WATERMARK", fill='gray', font=font)

# Save the image
image.save('test-image.png')
print("Test image created: test-image.png")
