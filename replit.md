# AI Watermark Remover

## Overview

This is a full-stack web application that provides AI-powered watermark removal services. Built with React/TypeScript on the frontend and Express.js on the backend, it offers a modern, responsive interface for users to upload images and remove watermarks using AI technology.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite for fast development and optimized builds
- **UI Library**: Radix UI components with shadcn/ui design system
- **Styling**: Tailwind CSS with custom CSS variables for theming
- **State Management**: TanStack Query for server state management
- **Routing**: Wouter for lightweight client-side routing
- **Internationalization**: React i18next for multi-language support

### Backend Architecture
- **Framework**: Express.js with TypeScript
- **File Upload**: Multer for handling multipart/form-data
- **API Integration**: TextIn API for watermark removal processing
- **Session Management**: Session-based authentication with PostgreSQL storage
- **Database**: PostgreSQL with Neon serverless database
- **ORM**: Drizzle ORM for type-safe database operations

## Key Components

### Database Schema
The application uses a single main table `processed_images` that tracks:
- Image metadata (filename, size, MIME type)
- Processing status (pending, processing, completed, failed)
- Processing results (processed file URLs, processing time)
- Error handling and metadata storage

### Storage Layer
- **Development**: In-memory storage implementation (`MemStorage`)
- **Production**: Database-backed storage with Drizzle ORM
- Interface-based design allows easy switching between storage implementations

### File Processing Pipeline
1. **Upload**: Images are uploaded via drag-and-drop or file picker
2. **Validation**: File type and size validation (50MB limit, JPG/PNG/BMP support)
3. **Processing**: Integration with TextIn API for watermark removal
4. **Storage**: Processed images are stored and metadata tracked
5. **Delivery**: Processed images are served back to users

### API Design
- RESTful API endpoints for image processing
- Comprehensive error handling and validation
- File upload handling with proper MIME type checking
- Status tracking for long-running operations

## Data Flow

1. **User Upload**: User drags/drops or selects images in the frontend
2. **Client Validation**: File type and size validation on the client
3. **API Request**: Files are sent to `/api/watermark-removal` endpoint
4. **Server Processing**: 
   - File validation and storage
   - Database record creation
   - TextIn API integration for processing
   - Status updates and result storage
5. **Response**: Processed image URLs and metadata returned to client
6. **UI Update**: Frontend updates with processing results

## External Dependencies

### Core Dependencies
- **Neon Database**: Serverless PostgreSQL database
- **TextIn API**: Third-party AI service for watermark removal
- **Radix UI**: Accessible component primitives
- **TanStack Query**: Server state management
- **Drizzle ORM**: Type-safe database operations

### Development Tools
- **Vite**: Build tool and development server
- **TypeScript**: Type safety and developer experience
- **Tailwind CSS**: Utility-first CSS framework
- **ESBuild**: Fast bundling for production

## Deployment Strategy

### Development
- Vite development server with hot module replacement
- In-memory storage for rapid prototyping
- Environment-based configuration

### Production
- **Build Process**: Vite builds frontend, ESBuild bundles backend
- **Static Assets**: Frontend built to `dist/public`
- **Server**: Express server serves both API and static files
- **Database**: PostgreSQL via Neon serverless platform
- **Environment**: Production configuration with proper error handling

### Configuration
- Environment variables for API keys and database URLs
- Separate development and production configurations
- Build-time optimization for performance

## User Preferences

Preferred communication style: Simple, everyday language.

## Changelog

Changelog:
- July 08, 2025. Initial setup