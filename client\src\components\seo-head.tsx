import { useTranslation } from 'react-i18next';
import { useLocation } from 'wouter';

interface SEOHeadProps {
  title?: string;
  description?: string;
  image?: string;
  url?: string;
}

export const SEOHead = ({ title, description, image, url }: SEOHeadProps) => {
  const { t } = useTranslation();
  const [location] = useLocation();

  const defaultTitle = t('header.title');
  const defaultDescription = t('hero.subtitle');
  const defaultImage = 'https://images.unsplash.com/photo-1677442136019-21780ecad995?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&h=630';
  const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://watermarkremover.ai';

  const seoTitle = title || defaultTitle;
  const seoDescription = description || defaultDescription;
  const seoImage = image || defaultImage;
  const seoUrl = url || `${baseUrl}${location}`;

  // Update document title
  if (typeof document !== 'undefined') {
    document.title = seoTitle;
  }

  return (
    <>
      {/* Basic Meta Tags */}
      <meta name="description" content={seoDescription} />
      <meta name="keywords" content="watermark removal, AI watermark remover, remove watermark online, image processing, photo editing" />
      <meta name="robots" content="index, follow" />
      <link rel="canonical" href={seoUrl} />

      {/* Open Graph / Facebook */}
      <meta property="og:type" content="website" />
      <meta property="og:url" content={seoUrl} />
      <meta property="og:title" content={seoTitle} />
      <meta property="og:description" content={seoDescription} />
      <meta property="og:image" content={seoImage} />
      <meta property="og:site_name" content="AI Watermark Remover" />

      {/* Twitter */}
      <meta property="twitter:card" content="summary_large_image" />
      <meta property="twitter:url" content={seoUrl} />
      <meta property="twitter:title" content={seoTitle} />
      <meta property="twitter:description" content={seoDescription} />
      <meta property="twitter:image" content={seoImage} />

      {/* Additional SEO */}
      <meta name="author" content="AI Watermark Remover" />
      <meta name="theme-color" content="#3b82f6" />
      
      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": "AI Watermark Remover",
          "description": seoDescription,
          "url": seoUrl,
          "applicationCategory": "ImageApplication",
          "operatingSystem": "Any",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          },
          "creator": {
            "@type": "Organization",
            "name": "AI Watermark Remover",
            "url": baseUrl
          }
        })}
      </script>
    </>
  );
};
