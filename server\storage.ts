import { processedImages, type ProcessedImage, type InsertProcessedImage } from "@shared/schema";

export interface IStorage {
  createProcessedImage(image: InsertProcessedImage): Promise<ProcessedImage>;
  getProcessedImage(id: number): Promise<ProcessedImage | undefined>;
  updateProcessedImage(id: number, updates: Partial<ProcessedImage>): Promise<ProcessedImage | undefined>;
  deleteProcessedImage(id: number): Promise<boolean>;
  getProcessedImagesByStatus(status: string): Promise<ProcessedImage[]>;
}

export class MemStorage implements IStorage {
  private processedImages: Map<number, ProcessedImage>;
  private currentId: number;

  constructor() {
    this.processedImages = new Map();
    this.currentId = 1;
  }

  async createProcessedImage(insertImage: InsertProcessedImage): Promise<ProcessedImage> {
    const id = this.currentId++;
    const now = new Date();
    const image: ProcessedImage = {
      ...insertImage,
      id,
      createdAt: now,
      updatedAt: now
    };
    this.processedImages.set(id, image);
    return image;
  }

  async getProcessedImage(id: number): Promise<ProcessedImage | undefined> {
    return this.processedImages.get(id);
  }

  async updateProcessedImage(id: number, updates: Partial<ProcessedImage>): Promise<ProcessedImage | undefined> {
    const existing = this.processedImages.get(id);
    if (!existing) return undefined;

    const updated: ProcessedImage = {
      ...existing,
      ...updates,
      updatedAt: new Date()
    };
    this.processedImages.set(id, updated);
    return updated;
  }

  async deleteProcessedImage(id: number): Promise<boolean> {
    return this.processedImages.delete(id);
  }

  async getProcessedImagesByStatus(status: string): Promise<ProcessedImage[]> {
    return Array.from(this.processedImages.values()).filter(
      image => image.status === status
    );
  }
}

export const storage = new MemStorage();
