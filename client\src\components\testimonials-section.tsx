import { useTranslation } from 'react-i18next';
import { Star } from 'lucide-react';

const testimonials = [
  {
    key: 0,
    avatar: 'SC',
    gradient: 'from-primary to-secondary',
  },
  {
    key: 1,
    avatar: 'MJ',
    gradient: 'from-secondary to-green-600',
  },
  {
    key: 2,
    avatar: 'EL',
    gradient: 'from-accent to-orange-600',
  },
];

export const TestimonialsSection = () => {
  const { t } = useTranslation();

  return (
    <section className="py-20 gradient-testimonials">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-6">
            {t('testimonials.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('testimonials.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div 
              key={testimonial.key}
              className={`glass-card p-8 rounded-2xl shadow-xl interactive-hover testimonial-card animate-slide-up animate-delay-${(index % 3 + 1) * 100} opacity-0`}
            >
              <div className="flex items-center mb-4">
                <div className="flex text-yellow-400">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 fill-current" />
                  ))}
                </div>
              </div>
              <p className="text-gray-700 mb-4">
                {t(`testimonials.reviews.${testimonial.key}.content`)}
              </p>
              <div className="flex items-center">
                <div className={`w-12 h-12 bg-gradient-to-r ${testimonial.gradient} rounded-full flex items-center justify-center text-white font-semibold animate-scale-in`}>
                  {testimonial.avatar}
                </div>
                <div className="ml-4">
                  <p className="font-semibold text-gray-900">
                    {t(`testimonials.reviews.${testimonial.key}.author`)}
                  </p>
                  <p className="text-sm text-gray-600">
                    {t(`testimonials.reviews.${testimonial.key}.role`)}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
