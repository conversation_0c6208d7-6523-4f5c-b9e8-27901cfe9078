import { useTranslation } from 'react-i18next';
import { Check } from 'lucide-react';
import { Button } from '@/components/ui/button';

const features = [
  'rest',
  'batch',
  'secure',
  'formats',
];

export const APISection = () => {
  const { t } = useTranslation();

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-6">
            {t('api.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('api.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <h3 className="text-2xl font-bold text-gray-900 mb-6">
              {t('api.features.title')}
            </h3>
            <div className="space-y-4">
              {features.map((feature) => (
                <div key={feature} className="flex items-start">
                  <div className="w-6 h-6 bg-secondary rounded-full flex items-center justify-center mt-1 mr-4">
                    <Check className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">
                      {t(`api.features.${feature}.title`)}
                    </h4>
                    <p className="text-gray-600">
                      {t(`api.features.${feature}.description`)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-8">
              <Button className="gradient-primary text-white px-8 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300 transform hover:scale-105 btn-primary">
                {t('api.getStarted')}
              </Button>
            </div>
          </div>

          <div>
            <div className="bg-gray-900 rounded-2xl p-6 text-green-400 font-mono text-sm overflow-x-auto">
              <div className="text-gray-500 mb-2">// API Example</div>
              <div className="text-white">curl -X POST \</div>
              <div className="text-white ml-2">https://api.textin.com/ai/service/v1/pdf_to_markdown \</div>
              <div className="text-white ml-2">-H "x-ti-app-id: d116412bff6bb3272cddfcd730bc99a8" \</div>
              <div className="text-white ml-2">-H "x-ti-secret-code: c1eddc385f74f8cc9a80ec3614a446c5" \</div>
              <div className="text-white ml-2">-H "Content-Type: application/octet-stream" \</div>
              <div className="text-white ml-2">--data-binary @image.jpg</div>
              <div className="text-gray-500 mt-4 mb-2">// Response</div>
              <div className="text-white">{'{'}</div>
              <div className="text-white ml-2">"code": 200,</div>
              <div className="text-white ml-2">"message": "success",</div>
              <div className="text-white ml-2">"result": {'{'}</div>
              <div className="text-white ml-4">"processed_image_url": "...",</div>
              <div className="text-white ml-4">"processing_time": 2.3</div>
              <div className="text-white ml-2">{'}'}</div>
              <div className="text-white">{'}'}</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
