import { useTranslation } from 'react-i18next';

const examples = [
  {
    key: 'professional',
    image: 'https://images.unsplash.com/photo-1542273917363-3b1817f69a2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300',
    alt: 'Professional photography before watermark removal',
  },
  {
    key: 'business',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300',
    alt: 'Business success photography after watermark removal',
  },
  {
    key: 'technology',
    image: 'https://images.unsplash.com/photo-1518186285589-2f7649de83e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300',
    alt: 'AI technology interface processing',
  },
];

export const GallerySection = () => {
  const { t } = useTranslation();

  return (
    <section className="py-20 gradient-testimonials">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-6">
            {t('gallery.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('gallery.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {examples.map((example) => (
            <div 
              key={example.key}
              className="bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-shadow duration-300 testimonial-card"
            >
              <img 
                src={example.image} 
                alt={example.alt}
                className="w-full h-48 object-cover"
                loading="lazy"
              />
              <div className="p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {t(`gallery.examples.${example.key}.title`)}
                </h3>
                <p className="text-gray-600">
                  {t(`gallery.examples.${example.key}.description`)}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};
